<!--Copyright 2023 The HuggingFace Team. All rights reserved.

Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
the License. You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.
-->

# Time Series Utilities

This page lists all the utility functions and classes that can be used for Time Series based models.

Most of those are only useful if you are studying the code of the time series models or you wish to add to the collection of distributional output classes.

## Distributional Output

[[autodoc]] time_series_utils.NormalOutput

[[autodoc]] time_series_utils.StudentTOutput

[[autodoc]] time_series_utils.NegativeBinomialOutput
