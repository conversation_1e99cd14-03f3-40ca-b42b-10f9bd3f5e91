<!--Copyright 2021 The HuggingFace Team. All rights reserved.

Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
the License. You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.
-->

# General Utilities

This page lists all of Transformers general utility functions that are found in the file `utils.py`.

Most of those are only useful if you are studying the general code in the library.


## Enums and namedtuples

[[autodoc]] utils.ExplicitEnum

[[autodoc]] utils.PaddingStrategy

[[autodoc]] utils.TensorType

## Special Decorators

[[autodoc]] utils.add_start_docstrings

[[autodoc]] utils.add_start_docstrings_to_model_forward

[[autodoc]] utils.add_end_docstrings

[[autodoc]] utils.add_code_sample_docstrings

[[autodoc]] utils.replace_return_docstrings

## Special Properties

[[autodoc]] utils.cached_property

## Other Utilities

[[autodoc]] utils._LazyModule
