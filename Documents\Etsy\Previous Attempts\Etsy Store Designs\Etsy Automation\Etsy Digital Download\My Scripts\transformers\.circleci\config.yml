version: 2.1
setup: true
orbs:
    continuation: circleci/continuation@0.1.0

parameters:
    nightly:
        type: boolean
        default: false

jobs:
    # Ensure running with CircleCI/huggingface
    check_circleci_user:
        docker:
            - image: cimg/python:3.7.12
        parallelism: 1
        steps:
            - run: echo $CIRCLE_PROJECT_USERNAME
            - run: |
                if [ "$CIRCLE_PROJECT_USERNAME" = "huggingface" ]; then
                    exit 0
                else
                    echo "The CI is running under $CIRCLE_PROJECT_USERNAME personal account. Please follow https://support.circleci.com/hc/en-us/articles/************-Troubleshooting-why-pull-requests-are-not-triggering-jobs-on-my-organization- to fix it."; exit -1
                fi
    # Fetch the tests to run
    fetch_tests:
        working_directory: ~/transformers
        docker:
            - image: cimg/python:3.7.12
        parallelism: 1
        steps:
            - checkout
            - run: pip install --upgrade pip
            - run: pip install GitPython
            - run: pip install .
            - run: mkdir -p test_preparation
            - run: python utils/tests_fetcher.py | tee tests_fetched_summary.txt
            - store_artifacts:
                  path: ~/transformers/tests_fetched_summary.txt
            - run: |
                if [ -f test_list.txt ]; then
                    cp test_list.txt test_preparation/test_list.txt
                else
                    touch test_preparation/test_list.txt
                fi
            - run: |
                if [ -f test_repo_utils.txt ]; then
                    mv test_repo_utils.txt test_preparation/test_repo_utils.txt
                else
                    touch test_preparation/test_repo_utils.txt
                fi
            - run: python utils/tests_fetcher.py --filter_tests
            - run: |
                if [ -f test_list.txt ]; then
                    mv test_list.txt test_preparation/filtered_test_list.txt
                else
                    touch test_preparation/filtered_test_list.txt
                fi
            - run: python utils/tests_fetcher.py --filters tests examples | tee examples_tests_fetched_summary.txt
            - run: |
                  if [ -f test_list.txt ]; then
                      mv test_list.txt test_preparation/examples_test_list.txt
                  else
                      touch test_preparation/examples_test_list.txt
                  fi
            - store_artifacts:
                  path: test_preparation/test_list.txt
            - store_artifacts:
                  path: ~/transformers/test_preparation/filtered_test_list.txt
            - store_artifacts:
                  path: test_preparation/examples_test_list.txt
            - run: python .circleci/create_circleci_config.py --fetcher_folder test_preparation
            - run: |
                  if [ ! -s test_preparation/generated_config.yml ]; then
                      echo "No tests to run, exiting early!"
                      circleci-agent step halt
                  fi
            - run: cp test_preparation/generated_config.yml test_preparation/generated_config.txt
            - store_artifacts:
                  path: test_preparation/generated_config.txt
            - continuation/continue:
                  configuration_path: test_preparation/generated_config.yml

    # To run all tests for the nightly build
    fetch_all_tests:
        working_directory: ~/transformers
        docker:
            - image: cimg/python:3.7.12
        parallelism: 1
        steps:
            - checkout
            - run: pip install --upgrade pip
            - run: pip install GitPython
            - run: pip install .
            - run: |
                  mkdir test_preparation
                  echo -n "tests" > test_preparation/test_list.txt
                  echo -n "tests" > test_preparation/examples_test_list.txt
                  echo -n "tests/repo_utils" > test_preparation/test_repo_utils.txt
            - run: |
                  echo -n "tests" > test_list.txt
                  python utils/tests_fetcher.py --filter_tests
                  mv test_list.txt test_preparation/filtered_test_list.txt
            - run: python .circleci/create_circleci_config.py --fetcher_folder test_preparation
            - run: cp test_preparation/generated_config.yml test_preparation/generated_config.txt
            - store_artifacts:
                  path: test_preparation/generated_config.txt
            - continuation/continue:
                  configuration_path: test_preparation/generated_config.yml

    check_code_quality:
        working_directory: ~/transformers
        docker:
            - image: cimg/python:3.7.12
        resource_class: large
        environment:
            TRANSFORMERS_IS_CI: yes
            PYTEST_TIMEOUT: 120
        parallelism: 1
        steps:
            - checkout
            - restore_cache:
                  keys:
                      - v0.5-code_quality-{{ checksum "setup.py" }}
                      - v0.5-code-quality
            - run: pip install --upgrade pip
            - run: pip install .[all,quality]
            - save_cache:
                  key: v0.5-code_quality-{{ checksum "setup.py" }}
                  paths:
                      - '~/.cache/pip'
            - run:
                name: Show installed libraries and their versions
                command: pip freeze | tee installed.txt
            - store_artifacts:
                  path: ~/transformers/installed.txt
            - run: black --check examples tests src utils
            - run: ruff examples tests src utils
            - run: python utils/custom_init_isort.py --check_only
            - run: python utils/sort_auto_mappings.py --check_only
            - run: doc-builder style src/transformers docs/source --max_len 119 --check_only --path_to_docs docs/source
            - run: python utils/check_doc_toc.py

    check_repository_consistency:
        working_directory: ~/transformers
        docker:
            - image: cimg/python:3.7.12
        resource_class: large
        environment:
            TRANSFORMERS_IS_CI: yes
            PYTEST_TIMEOUT: 120
        parallelism: 1
        steps:
            - checkout
            - restore_cache:
                  keys:
                      - v0.5-repository_consistency-{{ checksum "setup.py" }}
                      - v0.5-repository_consistency
            - run: pip install --upgrade pip
            - run: pip install .[all,quality]
            - save_cache:
                  key: v0.5-repository_consistency-{{ checksum "setup.py" }}
                  paths:
                      - '~/.cache/pip'
            - run:
                name: Show installed libraries and their versions
                command: pip freeze | tee installed.txt
            - store_artifacts:
                  path: ~/transformers/installed.txt
            - run: python utils/check_copies.py
            - run: python utils/check_table.py
            - run: python utils/check_dummies.py
            - run: python utils/check_repo.py
            - run: python utils/check_inits.py
            - run: python utils/check_config_docstrings.py
            - run: python utils/check_config_attributes.py
            - run: python utils/check_doctest_list.py
            - run: make deps_table_check_updated
            - run: python utils/tests_fetcher.py --sanity_check
            - run: python utils/update_metadata.py --check-only
            - run: python utils/check_task_guides.py

workflows:
    version: 2
    setup_and_quality:
        when:
            not: <<pipeline.parameters.nightly>>
        jobs:
            - check_circleci_user
            - check_code_quality
            - check_repository_consistency
            - fetch_tests

    nightly:
        when: <<pipeline.parameters.nightly>>
        jobs:
            - check_circleci_user
            - check_code_quality
            - check_repository_consistency
            - fetch_all_tests