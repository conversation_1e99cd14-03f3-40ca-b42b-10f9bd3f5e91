name: Model templates runner

on:
  repository_dispatch:
  schedule:
    - cron: "0 2 * * *"

jobs:
  run_tests_templates:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Install dependencies
        run: |
          sudo apt -y update && sudo apt install -y libsndfile1-dev

      - name: Load cached virtual environment
        uses: actions/cache@v2
        id: cache
        with:
          path: ~/venv/
          key: v4-tests_templates-${{ hashFiles('setup.py') }}

      - name: Create virtual environment on cache miss
        if: steps.cache.outputs.cache-hit != 'true'
        run: |
          python -m venv ~/venv && . ~/venv/bin/activate
          pip install --upgrade pip!=21.3
          pip install -e .[dev]

      - name: Check transformers location
        # make `transformers` available as package (required since we use `-e` flag) and check it's indeed from the repo.
        run: |
          . ~/venv/bin/activate
          python setup.py develop
          transformer_loc=$(pip show transformers | grep "Location: " | cut -c11-)
          transformer_repo_loc=$(pwd .)
          if [ "$transformer_loc" != "$transformer_repo_loc/src" ]; then
              echo "transformers is from $transformer_loc but it shoud be from $transformer_repo_loc/src."
              echo "A fix is required. Stop testing."
              exit 1
          fi

      - name: Create model files
        run: |
          . ~/venv/bin/activate
          transformers-cli add-new-model --testing --testing_file=templates/adding_a_new_model/tests/encoder-bert-tokenizer.json --path=templates/adding_a_new_model
          transformers-cli add-new-model --testing --testing_file=templates/adding_a_new_model/tests/pt-encoder-bert-tokenizer.json --path=templates/adding_a_new_model
          transformers-cli add-new-model --testing --testing_file=templates/adding_a_new_model/tests/standalone.json --path=templates/adding_a_new_model
          transformers-cli add-new-model --testing --testing_file=templates/adding_a_new_model/tests/tf-encoder-bert-tokenizer.json --path=templates/adding_a_new_model
          transformers-cli add-new-model --testing --testing_file=templates/adding_a_new_model/tests/tf-seq-2-seq-bart-tokenizer.json --path=templates/adding_a_new_model
          transformers-cli add-new-model --testing --testing_file=templates/adding_a_new_model/tests/pt-seq-2-seq-bart-tokenizer.json --path=templates/adding_a_new_model
          transformers-cli add-new-model --testing --testing_file=templates/adding_a_new_model/tests/flax-encoder-bert-tokenizer.json --path=templates/adding_a_new_model
          transformers-cli add-new-model --testing --testing_file=templates/adding_a_new_model/tests/flax-seq-2-seq-bart-tokenizer.json --path=templates/adding_a_new_model
          make style
          python utils/check_table.py --fix_and_overwrite
          python utils/check_dummies.py --fix_and_overwrite
          python utils/check_copies.py --fix_and_overwrite

      - name: Run all non-slow tests
        run: |
          . ~/venv/bin/activate
          python -m pytest -n 2 --dist=loadfile -s --make-reports=tests_templates tests/*template*

      - name: Run style changes
        run: |
          . ~/venv/bin/activate
          make style && make quality && make repo-consistency

      - name: Failure short reports
        if: ${{ always() }}
        run: cat reports/tests_templates/failures_short.txt

      - name: Test suite reports artifacts
        if: ${{ always() }}
        uses: actions/upload-artifact@v3
        with:
          name: run_all_tests_templates_test_reports
          path: reports/tests_templates
